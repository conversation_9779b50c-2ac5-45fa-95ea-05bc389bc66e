import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import {
  Card,
  Button,
  Form,
  Input,
  Select,
  Space,
  Typography,
  Alert,
  Progress,
  message,
  Row,
  Col,
  InputNumber,
  DatePicker,
  Switch
} from 'antd';
import {
  FileWordOutlined,
  SendOutlined,
  ReloadOutlined,
  SaveOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import http from '../../libs/http';
import GpuSelector from '../../components/GpuSelector';
import SimpleWordEditor from './components/SimpleWordEditor';


const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

export default observer(function DocumentGenerator() {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateVariables, setTemplateVariables] = useState([]);
  const [generating, setGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [draftId, setDraftId] = useState(null);
  const [richTextValues, setRichTextValues] = useState({}); // 存储富文本编辑器的值

  // 编辑模式相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingInstanceId, setEditingInstanceId] = useState(null);
  const [editingInstance, setEditingInstance] = useState(null);

  const [form] = Form.useForm();

  // 解析URL参数，检查是否为编辑模式
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    const instanceId = urlParams.get('instanceId');
    const templateId = urlParams.get('templateId');

    if (mode === 'edit' && instanceId && templateId) {
      setIsEditMode(true);
      setEditingInstanceId(instanceId);
      // 先获取模板列表，然后加载编辑数据
      fetchTemplates().then((templateList) => {
        loadEditingInstance(instanceId, templateId, templateList);
      });
    } else {
      fetchTemplates();
    }
  }, []);

  // 加载编辑实例数据
  const loadEditingInstance = async (instanceId, templateId, templateList = null) => {
    try {
      setLoading(true);

      // 获取文档实例数据
      const instanceRes = await http.get(`/api/model-storage/document-instances/${instanceId}/`);
      const instance = instanceRes.data || instanceRes;
      setEditingInstance(instance);
      setDraftId(instanceId);

      // 从模板列表中找到对应的模板对象并设置
      const templatesSource = templateList || templates;
      const template = templatesSource.find(t => t.id === parseInt(templateId));
      if (template) {
        setSelectedTemplate(template);
      }

      // 获取模板变量，在编辑模式下通知API不返回默认值
      await fetchTemplateVariables(templateId, false, true);

      // 解析变量值
      let variablesValues = {};
      if (typeof instance.variables_values === 'string') {
        try {
          variablesValues = JSON.parse(instance.variables_values || '{}');
        } catch (parseError) {
          variablesValues = {};
        }
      } else if (typeof instance.variables_values === 'object' && instance.variables_values !== null) {
        variablesValues = instance.variables_values;
      }

      // 设置表单值
      const formValues = {
        title: instance.title,
        rich_content: instance.rich_content || '',
        template_id: template ? template.id : parseInt(templateId) // 设置模板ID
      };

      // 设置变量值
      Object.keys(variablesValues).forEach(key => {
        formValues[`var_${key}`] = variablesValues[key];
      });

      // 设置富文本编辑器的值
      const richTextInitialValues = {};
      Object.keys(variablesValues).forEach(key => {
        richTextInitialValues[`var_${key}`] = variablesValues[key] || '';
      });
      richTextInitialValues.rich_content = instance.rich_content || '';
      setRichTextValues(richTextInitialValues);

      // 设置表单值
      form.setFieldsValue(formValues);

      message.success('编辑数据加载成功');
    } catch (error) {
      message.error('加载编辑数据失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const res = await http.get('/api/model-storage/word-templates/');

      // 由于http拦截器会直接返回data字段，所以res就是数据数组
      const templates = Array.isArray(res) ? res : (res.data || []);
      const activeTemplates = templates.filter(t => t.status === 'active');

      setTemplates(activeTemplates);
      return activeTemplates; // 返回模板列表
    } catch (error) {
      message.error('获取模板列表失败：' + error.message);
      return []; // 出错时返回空数组
    }
  };

  const fetchTemplateVariables = async (templateId, skipDefaultValues = false, isEditMode = false) => {
    try {
      // 在编辑模式下，通知API不返回默认值
      const apiUrl = isEditMode
        ? `/api/model-storage/word-templates/?id=${templateId}&edit_mode=true`
        : `/api/model-storage/word-templates/?id=${templateId}`;

      const res = await http.get(apiUrl);

      // 由于http拦截器会直接返回data字段，所以res就是模板对象
      const templateData = res;
      const variables = templateData.variables || [];

      setTemplateVariables(variables);

      // 只在非编辑模式或明确要求时设置默认值
      if (!skipDefaultValues) {
        // 设置变量默认值
        const defaultValues = {};
        const richTextDefaultValues = {};

        variables.forEach(variable => {
          const fieldName = `var_${variable.variable_name}`;

          // 直接使用API返回的默认值，不管是否为空
          defaultValues[fieldName] = variable.default_value || '';

          // 检查是否是富文本变量
          const isRichTextVariable = variable.variable_type === 'richtext';

          if (isRichTextVariable) {
            richTextDefaultValues[fieldName] = variable.default_value || '';
          }
        });

        // 设置表单默认值
        if (Object.keys(defaultValues).length > 0) {
          const currentValues = form.getFieldsValue();
          form.setFieldsValue({
            ...currentValues,
            ...defaultValues
          });
        }

        // 设置富文本编辑器默认值
        if (Object.keys(richTextDefaultValues).length > 0) {
          setRichTextValues(prev => ({
            ...prev,
            ...richTextDefaultValues
          }));
        }
      }
    } catch (error) {
      message.error('获取模板变量失败：' + error.message);
    }
  };

  const handleTemplateSelect = async (templateId) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template);

    if (template) {
      // 清空之前的富文本值，避免不同模板间的数据混淆
      setRichTextValues({});

      // 先设置基本的模板信息
      form.setFieldsValue({
        template_id: templateId,
        title: template.name // 默认使用模板名称作为文档标题
      });

      // 然后获取并设置变量默认值（这样不会被覆盖）
      await fetchTemplateVariables(templateId);

      // 清空之前的草稿ID，因为选择了新模板
      setDraftId(null);
      setLastSaved(null);
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      setSaving(true);

      // 获取表单数据
      const values = form.getFieldsValue();

      // 检查是否有内容需要保存
      const hasContent = values.title ||
                        Object.keys(values).some(key =>
                          key.startsWith('var_') && values[key]
                        );

      if (!hasContent) {
        message.warning('请先填写内容再保存草稿');
        setSaving(false);
        return;
      }

      // 提取变量值，包括富文本编辑器的值
      const variables = {};
      Object.keys(values).forEach(key => {
        if (key.startsWith('var_')) {
          const varName = key.substring(4);
          // 优先使用富文本编辑器的值
          variables[varName] = richTextValues[key] || values[key];
        }
      });

      const draftData = {
        title: values.title || '未命名草稿',
        template_id: isEditMode ? selectedTemplate?.id : values.template_id,
        variables_values: variables,
        rich_content: richTextValues.rich_content || values.rich_content || '',
        status: 'draft'
      };

      let response;
      if (draftId || isEditMode) {
        // 更新现有草稿 - 将ID放在数据中
        const updateId = draftId || editingInstanceId;
        response = await http.put(`/api/model-storage/document-instances/${updateId}/`, {
          ...draftData,
          id: updateId
        });
      } else {
        // 创建新草稿
        response = await http.post('/api/model-storage/document-instances/', draftData);
        setDraftId(response.id);
      }

      setLastSaved(new Date());
      message.success(isEditMode ? '文档更新成功' : '草稿保存成功');
    } catch (error) {
      message.error('保存草稿失败：' + error.message);
    } finally {
      setSaving(false);
    }
  };

  // 自动保存草稿已禁用 - 改为手动保存避免生成过多实例
  // useEffect(() => {
  //   if (!selectedTemplate) return;

  //   const autoSaveInterval = setInterval(() => {
  //     const values = form.getFieldsValue();
  //     // 检查是否有内容需要保存
  //     const hasContent = values.title ||
  //                       Object.keys(values).some(key =>
  //                         key.startsWith('var_') && values[key]
  //                       );

  //     if (hasContent) {
  //       handleSaveDraft();
  //     }
  //   }, 30000); // 30秒自动保存

  //   return () => clearInterval(autoSaveInterval);
  // }, [selectedTemplate, draftId]);

  const handleGenerate = async () => {
    try {
      setGenerating(true);
      setGenerationProgress(0);
      
      // 验证表单
      const values = await form.validateFields();

      // 提取变量值
      const variables = {};
      Object.keys(values).forEach(key => {
        if (key.startsWith('var_')) {
          const varName = key.substring(4); // 移除 'var_' 前缀
          variables[varName] = values[key];
        }
      });
      
      // 模拟生成进度
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);
      
      // 调用生成API
      const generateData = {
        template_id: values.template_id,
        title: values.title,
        variables: variables,
        content: values.content || ''
      };

      const response = await http.post('/api/model-storage/word-templates/generate/', generateData);

      clearInterval(progressInterval);
      setGenerationProgress(100);
      
      // 下载文件
      if (response.download_url) {
        const link = document.createElement('a');
        link.href = response.download_url;
        link.download = `${values.title}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        message.success('文档生成成功！');
      } else {
        message.error('文档生成失败：未获取到下载链接');
      }

    } catch (error) {
      message.error('文档生成失败：' + error.message);
    } finally {
      setGenerating(false);
      setTimeout(() => setGenerationProgress(0), 2000);
    }
  };

  const renderVariableInput = (variable) => {
    const fieldName = `var_${variable.variable_name}`;
    // 如果display_name是通用的"富文本内容"，则使用variable_name
    let displayName = variable.display_name || variable.variable_name || '未命名字段';
    if (displayName === '富文本内容' || displayName === '富文本') {
      displayName = variable.variable_name;
    }

    // 检查是否是GPU相关变量
    const isGpuVariable = variable.variable_name.toLowerCase().includes('gpu') ||
                         displayName.toLowerCase().includes('gpu') ||
                         displayName.toLowerCase().includes('加速卡');

    // 检查是否是富文本变量 - 只有明确标记为richtext或包含"正文"的才是富文本
    const isRichTextVariable = variable.variable_type === 'richtext' ||
                              variable.variable_name.toLowerCase().includes('正文') ||
                              displayName.toLowerCase().includes('正文');

    // 富文本变量单独处理，不在这里渲染
    if (isRichTextVariable) {
      return null;
    }

    switch (variable.variable_type) {
      case 'number':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <InputNumber
              placeholder={`请输入${displayName}`}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请选择${displayName}` }]}
            tooltip={variable.description}
          >
            <DatePicker
              placeholder={`请选择${displayName}`}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'boolean':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            valuePropName="checked"
            tooltip={variable.description}
          >
            <Switch />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <TextArea
              rows={4}
              placeholder={`请输入${displayName}`}
            />
          </Form.Item>
        );

      default:
        // GPU变量使用GPU选择器
        if (isGpuVariable) {
          return (
            <Form.Item
              key={fieldName}
              name={fieldName}
              label={displayName}
              rules={[{ required: variable.is_required, message: `请选择${displayName}` }]}
              tooltip={variable.description}
            >
              <GpuSelector
                placeholder={`请选择${displayName}`}
                style={{ width: '100%' }}
              />
            </Form.Item>
          );
        }

        // 普通文本输入
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <Input
              placeholder={`请输入${displayName}`}
            />
          </Form.Item>
        );
    }
  };

  // 渲染富文本变量 - 只有明确的富文本变量
  const renderRichTextVariables = () => {
    const richTextVariables = templateVariables.filter(variable => {
      const displayName = variable.display_name || variable.variable_name;
      // 只识别明确标记为richtext或包含"正文"的变量
      return variable.variable_type === 'richtext' ||
             variable.variable_name.toLowerCase().includes('正文') ||
             displayName.toLowerCase().includes('正文');
    });

    if (richTextVariables.length === 0) {
      return null;
    }

    return richTextVariables.map(variable => {
      const fieldName = `var_${variable.variable_name}`;
      // 如果display_name是通用的"富文本内容"，则使用variable_name
      let displayName = variable.display_name || variable.variable_name;
      if (displayName === '富文本内容' || displayName === '富文本') {
        displayName = variable.variable_name;
      }

      // 检查是否是"正文"相关的变量（需要完整的富文本编辑器）
      const isRichTextContent = variable.variable_name.toLowerCase().includes('正文') ||
                               displayName.toLowerCase().includes('正文');

      return (
        <Form.Item
          key={fieldName}
          name={fieldName}
          label={displayName}
          rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
          tooltip={variable.description}
        >
          <SimpleWordEditor
            placeholder={`请输入${displayName}`}
            height={250}
            value={richTextValues[fieldName] || ''}
            onChange={(value) => {
              // 编辑器变化时同步到表单
              form.setFieldsValue({ [fieldName]: value });
              setRichTextValues(prev => ({
                ...prev,
                [fieldName]: value
              }));
            }}
          />
        </Form.Item>
      );
    });
  };

  // 按组分类变量
  const groupVariables = () => {
    const groups = {};
    const regularVariables = templateVariables.filter(variable => {
      const displayName = variable.display_name || variable.variable_name;
      // 与renderVariableInput保持一致的富文本识别逻辑 - 只有明确标记为richtext或包含"正文"的才是富文本
      const isRichText = variable.variable_type === 'richtext' ||
                        variable.variable_name.toLowerCase().includes('正文') ||
                        displayName.toLowerCase().includes('正文');
      return !isRichText;
    });

    regularVariables.forEach(variable => {
      const groupName = variable.group_name || '其他';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(variable);
    });

    return groups;
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <Card 
        style={{ maxWidth: '1200px', margin: '0 auto' }}
        title={
          <Space>
            <FileWordOutlined style={{ color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>
              {isEditMode ? '编辑文档' : 'Word文档生成器'}
            </Title>
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchTemplates}
            loading={loading}
          >
            刷新模板
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
        >
          {/* 基本信息区域 */}
          <Card 
            title="基本信息" 
            size="small" 
            style={{ marginBottom: '16px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="template_id"
                  label="选择模板"
                  rules={[{ required: true, message: '请选择模板' }]}
                >
                  <Select
                    placeholder="请选择Word模板"
                    onChange={handleTemplateSelect}
                    size="large"
                    loading={loading}
                    disabled={isEditMode}
                  >
                    {templates.map(template => (
                      <Option key={template.id} value={template.id}>
                        <Space>
                          <FileWordOutlined />
                          <div>
                            <div>{template.name}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {template.original_filename && (
                                <span>文件: {template.original_filename} | </span>
                              )}
                              {template.description}
                            </Text>
                          </div>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="title"
                  label="文档标题"
                  rules={[{ required: true, message: '请输入文档标题' }]}
                >
                  <Input 
                    placeholder="请输入生成文档的标题" 
                    size="large"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 模板变量区域 */}
          {selectedTemplate && (
            <>
              {templateVariables.length > 0 ? (
                <>
                  {/* 按组显示普通变量 */}
                  {Object.entries(groupVariables()).map(([groupName, variables]) => (
                    <Card
                      key={groupName}
                      title={`${groupName} (${variables.length}个变量)`}
                      size="small"
                      style={{ marginBottom: '16px' }}
                    >
                      <Row gutter={16}>
                        {variables.map((variable) => {
                          const renderedInput = renderVariableInput(variable);
                          if (!renderedInput) return null;

                          return (
                            <Col
                              key={variable.variable_name}
                              span={variables.length === 1 ? 24 : (variables.length === 2 ? 12 : 8)}
                            >
                              {renderedInput}
                            </Col>
                          );
                        })}
                      </Row>
                    </Card>
                  ))}

                  {/* 富文本变量区域 */}
                  {renderRichTextVariables() && (
                    <Card
                      title="富文本"
                      size="small"
                      style={{ marginBottom: '16px' }}
                    >
                      {renderRichTextVariables()}
                    </Card>
                  )}
                </>
              ) : (
                <Card
                  title="模板变量配置"
                  size="small"
                  style={{ marginBottom: '16px' }}
                >
                  <Alert
                    message="调试信息"
                    description={
                      <div>
                        <p>模板已选择但没有变量数据</p>
                        <p>请打开浏览器控制台查看详细日志</p>
                        <p>模板ID: {selectedTemplate.id}</p>
                        <p>模板名称: {selectedTemplate.name}</p>
                      </div>
                    }
                    type="warning"
                    showIcon
                  />
                </Card>
              )}
            </>
          )}

          {/* 附加内容区域 - 如果没有富文本变量，提供一个通用的富文本输入 */}
          {selectedTemplate && renderRichTextVariables() === null && (
            <Card
              title="附加内容 (可选)"
              size="small"
              style={{ marginBottom: '16px' }}
            >
              <Form.Item
                name="content"
                label="附加富文本内容"
                tooltip="可以添加额外的富文本内容到文档中，将显示在框框里"
              >
                <SimpleWordEditor
                  placeholder="请输入要添加到文档中的额外内容..."
                  height={250}
                  value={richTextValues.content || ''}
                  onChange={(value) => {
                    // 避免循环更新：只有当值真正改变时才更新
                    if (value !== richTextValues.content) {
                      form.setFieldsValue({ content: value });
                      setRichTextValues(prev => ({
                        ...prev,
                        content: value
                      }));
                    }
                  }}
                />
              </Form.Item>
            </Card>
          )}

          {/* 生成按钮和进度 */}
          <Card size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              {generating && (
                <Progress
                  percent={generationProgress}
                  status={generationProgress === 100 ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              )}

              <div style={{ textAlign: 'center' }}>
                <Space size="large">
                  <Button
                    icon={<SaveOutlined />}
                    loading={saving}
                    onClick={handleSaveDraft}
                    disabled={!selectedTemplate || generating}
                    title="手动保存草稿，不会自动保存"
                  >
                    {saving ? '保存中...' : (isEditMode ? '更新文档' : '保存草稿')}
                  </Button>

                  <Button
                    type="primary"
                    size="large"
                    icon={<SendOutlined />}
                    loading={generating}
                    onClick={handleGenerate}
                    disabled={!selectedTemplate}
                    style={{ minWidth: '200px' }}
                  >
                    {generating ? '正在生成文档...' : '生成Word文档'}
                  </Button>
                </Space>
              </div>

              {/* 草稿保存状态 */}
              {lastSaved && (
                <div style={{ textAlign: 'center', marginTop: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <ClockCircleOutlined style={{ marginRight: '4px' }} />
                    最后保存: {lastSaved.toLocaleTimeString()}
                    {draftId && ` (草稿ID: ${draftId})`}
                  </Text>
                </div>
              )}
            </Space>
          </Card>
        </Form>

        {/* 模板信息展示 */}
        {selectedTemplate && (
          <Alert
            style={{ marginTop: '16px' }}
            message="当前选择的模板"
            description={
              <div>
                <p><strong>模板名称：</strong>{selectedTemplate.name}</p>
                <p><strong>原文件名：</strong>{selectedTemplate.original_filename || '未知'}</p>
                <p><strong>模板描述：</strong>{selectedTemplate.description}</p>
                <p><strong>模板类型：</strong>{selectedTemplate.template_type}</p>
                <p><strong>变量数量：</strong>{templateVariables.length}个</p>
              </div>
            }
            type="info"
            showIcon
          />
        )}
      </Card>
    </div>
  );
});
